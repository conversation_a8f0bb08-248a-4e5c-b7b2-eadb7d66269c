import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { TcpScaleForwarderService } from './tcp-scale-forwarder.service';

@Controller('scale')
export class WeightController {
  constructor(private readonly tcpScaleForwarderService: TcpScaleForwarderService) {}

  @Get('connection-status')
  checkDeviceConnection(
    @Query('deviceIp') deviceIp: string,
    @Query('devicePort') devicePort: string,
  ) {
    const isConnected = this.tcpScaleForwarderService.checkDeviceConnection(
      deviceIp,
      Number(devicePort),
    );
    
    return {
      deviceIp: deviceIp,
      devicePort: Number(devicePort),
      connected: isConnected,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('reconnect')
  async reconnectDevice(@Body() data: any) {
    const success = await this.tcpScaleForwarderService.reconnectDevice(
      data.deviceIp,
      Number(data.devicePort),
    );
    
    return {
      deviceIp: data.deviceIp,
      devicePort: Number(data.devicePort),
      success,
      message: success 
        ? `Thiết bị ${data.deviceIp}:${data.devicePort} đã được kết nối lại thành công.`
        : `Không thể kết nối lại thiết bị ${data.deviceIp}:${data.devicePort}.`,
      timestamp: new Date().toISOString(),
    };
  }
}