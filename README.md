# ape_mes_scale

## 📋Giới thiệu

`ape_mes_scale` là hệ thống trung gian kết nối và chuyển tiếp dữ liệu cân điện tử cho các hệ thống quản lý sản xuất (MES). Ứng dụng được xây dựng trên nền tảng NestJS, hỗ trợ kết nối nhiều thiết bị cân, thu thập và gửi dữ liệu cân về server thông qua API bảo mật.

## 🏗️Kiến trúc hệ thống

- **NestJS**: <PERSON> chính, tổ chức theo module, controller, service.
- **TCP Socket**: Kết nối tới các thiết bị cân điện tử qua giao thức TCP.
- **API Helper**: Gửi dữ liệu cân và lấy danh sách thiết bị qua API (c<PERSON> thể bảo mật bằng header).
- **Docker**: Hỗ trợ đóng gói và triển khai ứng dụng dễ dàng.

## 📁Cấu trúc thư mục

### 🗂️Thư mục gốc
- `<PERSON>erfile`: Đóng gói ứng dụng với Docker.
- `package.json`: Quản lý dependencies và script.
- `tsconfig.json`, `tsconfig.build.json`: Cấu hình TypeScript.
- `nest-cli.json`: Cấu hình cho Nest CLI.
- `README.md`: Tài liệu dự án.
- `dist/`: (Sinh ra sau khi build) Chứa mã nguồn đã biên dịch.
- `node_modules/`: (Sinh ra sau khi cài đặt) Chứa các thư viện phụ thuộc.

### 🗂️Thư mục src/ (Source Code chính)
- `main.ts`: Điểm khởi động ứng dụng.
- `modules/`: Các module chính của hệ thống.
  - `app.module.ts`, `app.controller.ts`, `app.service.ts`: Module và service chính.
  - `common/`: Các service dùng chung (ví dụ: `tcp-scale-forwarder.service.ts` để kết nối cân, `weight.module.ts`).
- `helpers/`: Các hàm tiện ích, ví dụ `apiPublicHelper.ts` để gọi API (POST/GET, có hoặc không có xác thực).
- `interfaces/`: Định nghĩa các interface TypeScript dùng chung.
- `subscribers/`: Các subscriber cho event hoặc entity.
- `common/filters/`: Bộ lọc exception toàn cục.

## 🚀Cài đặt và chạy dự án

### Yêu cầu hệ thống
- Node.js >= 16
- npm >= 8
- Docker (nếu chạy bằng container)

### Cài đặt
```bash
yarn install
```

### Chạy ở môi trường development
```bash
yarn start:dev
```

### Build production
```bash
yarn build
```

### Chạy production
```bash
yarn start:prod
```

## 🐳Sử dụng Docker

### Build image
```bash
docker build -t ape_mes_scale .
```

### Chạy container
```bash
docker run -d --name ape_mes_scale -p 3000:3000 --env-file .env ape_mes_scale
```

## 🔐Bảo mật
- Các API gửi/nhận dữ liệu đều sử dụng header `x-header` với giá trị bí mật (`API_SCALE_SECRET`) để xác thực.
- Không có key sẽ bị từ chối truy cập (Unauthorized).
- Có thể cấu hình biến môi trường trong file `.env`.

---


