import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import * as net from 'net'
import { apiPublicHelper } from 'src/helpers/apiPublicHelper'

@Injectable()
export class TcpScaleForwarderService implements OnModuleInit {
  private readonly logger = new Logger(TcpScaleForwarderService.name)
  private clients: { deviceIp: string; devicePort: number; socket: net.Socket; reconnectAttempts: number }[] = [];
  private readonly API_ENDPOINT = process.env.HOST_API + '/api/integration/scale/get-weight'
  private readonly API_GET_DEVICE = process.env.HOST_API + '/api/integration/scale/get-devices'

  private lastWeight: string | null = null 

  async onModuleInit() {
    await this.connectToAllScales();
  }

  // Cron job chạy mỗi giờ một lần để cập nhật danh sách thiết bị
  @Cron(CronExpression.EVERY_HOUR)
  async refreshDeviceList() {
    await this.connectToAllScales();
  }

  private async connectToAllScales() {
    try {
      const devices = await this.getDevices();
      if (!Array.isArray(devices)) {
        this.logger.error('❌ Danh sách thiết bị không hợp lệ');
        return;
      }
      for (const device of devices) {
        const deviceIp = device.ipAddress;
        const devicePort = Number(device.devicePort);
        if (!deviceIp || !devicePort) continue;
        this.connectToScale(deviceIp, devicePort);
      }
    } catch (err) {
      this.logger.error('❌ Không thể lấy danh sách thiết bị để kết nối');
    }
  }

  /**
   * Kết nối tới một thiết bị cân điện tử qua TCP socket.
   * Lắng nghe dữ liệu trả về từ cân, nhận dạng loại dữ liệu (SICS hoặc Toledo),
   * đóng gói payload và gửi lên server nếu giá trị cân thay đổi.
   * Tự động reconnect nếu mất kết nối.
   * @param deviceIp Địa chỉ IP của thiết bị cân
   * @param devicePort Cổng TCP của thiết bị cân
   */
  private connectToScale(deviceIp: string, devicePort: number, reconnectAttempts = 0) {
    this.logger.log(`🔄 Đang thử kết nối tới thiết bị ${deviceIp}:${devicePort}...`);

    const client = new net.Socket();
    let connectionClosed = false;
    
    client.setTimeout(10000);
    
    client.connect(devicePort, deviceIp, () => {
      this.logger.log(`✅ Đã kết nối tới cân tại ${deviceIp}:${devicePort}`);
      // Reset số lần thử lại khi kết nối thành công
      const existingClientIndex = this.clients.findIndex(c => c.deviceIp === deviceIp && c.devicePort === devicePort);
      if (existingClientIndex >= 0) {
        this.clients[existingClientIndex].reconnectAttempts = 0;
      }
    });
    
    client.on('timeout', () => {
      this.logger.error(`⏱️ Timeout khi kết nối tới ${deviceIp}:${devicePort}`);
      connectionClosed = true;
      client.destroy();
    });
    
    client.on('data', async (data: Buffer) => {
      this.logger.log(`📥 Nhận dữ liệu từ ${deviceIp}:${devicePort}: ${data.toString('hex')}`);
      // Chuyển dữ liệu nhận được thành chuỗi ASCII
      const text = data.toString('ascii').trim();
      let payload = null;
      // Xử lý dữ liệu dạng SICS
      if (text.startsWith('S_S') || text.startsWith('S_D')) {
        const parts = text.split('_');
        const weight = parts[parts.length - 2];
        const unit = parts[parts.length - 1];
        payload = {
          type: 'SICS',
          raw: text,
          weight,
          unit,
          timestamp: new Date().toISOString(),
        };
        // Gửi dữ liệu lên server nếu giá trị cân thay đổi
        if (this.lastWeight !== weight) {
          this.lastWeight = weight;
          this.sendDataToApi({ ...payload, deviceIp, devicePort });
        }
      }
      // Xử lý dữ liệu dạng Toledo (bắt đầu bằng ký tự STX 0x02)
      if (data[0] === 0x02) {
        const weight = data.slice(4, 10).toString().trim();
        payload = {
          type: 'TOLEDO_CONTINUOUS',
          raw: text,
          weight,
          unit: 'kg',
          timestamp: new Date().toISOString(),
        };
        // Gửi dữ liệu lên server nếu giá trị cân thay đổi
        if (this.lastWeight !== weight) {
          this.lastWeight = weight;
          this.sendDataToApi({ ...payload, deviceIp, devicePort });
        }
      }
    });
    client.on('error', (err) => {
      this.logger.error(`❌ Lỗi kết nối (${deviceIp}:${devicePort}): ${err.message}`);
    });
    client.on('close', () => {
      this.logger.log(`🔌 Kết nối đến ${deviceIp}:${devicePort} đã đóng. Đang thử kết nối lại...`);
      // Luôn thử kết nối lại mà không giới hạn số lần
      setTimeout(() => {
        if (!connectionClosed) {
          this.connectToScale(deviceIp, devicePort, reconnectAttempts + 1);
        }
      }, 5000); // Đợi 5 giây trước khi kết nối lại
    });
    
    // Lưu lại client socket để quản lý, kèm theo số lần thử lại
    const existingClientIndex = this.clients.findIndex(c => c.deviceIp === deviceIp && c.devicePort === devicePort);
    if (existingClientIndex >= 0) {
      this.clients[existingClientIndex] = { deviceIp, devicePort, socket: client, reconnectAttempts };
    } else {
      this.clients.push({ deviceIp, devicePort, socket: client, reconnectAttempts });
    }
  }

  private async sendDataToApi(payload: any) {
    try {
      this.logger.log(`📤 Gửi dữ liệu cân: ${JSON.stringify(payload)}`);
      await apiPublicHelper.processCallApiHelper(
        payload, // payload đã có deviceIp, devicePort
        this.API_ENDPOINT,
      )
    } catch (err: any) {
      console.log(err)
      this.logger.error(`❌ Lỗi gửi dữ liệu: ${err.message}`)
      if (err.response) {
        this.logger.error(`❌ Response data: ${JSON.stringify(err.response.data || {})}`)
      }
    }
  }

  private async getDevices() {
    try {
      this.logger.log(`🔎 Gọi API lấy danh sách thiết bị: ${this.API_GET_DEVICE}`)
      const response = await apiPublicHelper.processCallApiGetHelper(this.API_GET_DEVICE);
      return response;
    } catch (err: any) {
      this.logger.error(`❌ Lỗi lấy danh sách thiết bị: ${err.message}`)
      if (err.response) {
        this.logger.error(`❌ Response data: ${JSON.stringify(err.response)}`)
      }
      throw err
    }
  }

  /**
   * Kiểm tra trạng thái kết nối của một thiết bị.
   * @param deviceIp ID của thiết bị cần kiểm tra
   * @param port Cổng của thiết bị cần kiểm tra
   * @returns `true` nếu thiết bị đang được kết nối, `false` nếu không.
   */
  public checkDeviceConnection(deviceIp: string, devicePort: number): boolean {
    const client = this.clients.find(c => c.deviceIp === deviceIp && c.devicePort === devicePort);
    if (client) {
      // 'open' cho biết socket đang mở và kết nối
      this.logger.debug(`Trạng thái kết nối của thiết bị ${deviceIp}:${devicePort}: ${client.socket.readyState}`);
      return client.socket.readyState === 'open';
    }
    this.logger.warn(`Thiết bị ${deviceIp}:${devicePort} không tìm thấy trong danh sách client đã kết nối.`);
    return false;
  }

  public async reconnectDevice(deviceIp: string, devicePort: number): Promise<boolean> {
    const isConnected = this.checkDeviceConnection(deviceIp, devicePort);
    if (isConnected) {
      this.logger.log(`Thiết bị ${deviceIp}:${devicePort} đã được kết nối.`);
      return true;
    }
  
    try {
      const devices = await this.getDevices();
      const deviceToReconnect = devices.find((d: any) => 
        d.ipAddress === deviceIp && Number(d.devicePort) === devicePort
      );
  
      if (deviceToReconnect) {
        this.logger.log(`Bắt đầu kết nối lại cho thiết bị ${deviceIp}:${devicePort}...`);
        this.connectToScale(deviceIp, devicePort, 0);
        return true;
      } else {
        this.logger.warn(`Không tìm thấy thông tin thiết bị ${deviceIp}:${devicePort} để kết nối lại.`);
        return false;
      }
    } catch (error: any) {
      this.logger.error(`Lỗi khi tìm thông tin thiết bị để kết nối lại ${deviceIp}:${devicePort}: ${error.message}`);
      return false;
    }
  }
}
